{"compilerOptions": {"noEmitOnError": true, "resolveJsonModule": true, "target": "es6", "module": "commonjs", "lib": ["ESNext"], "allowJs": true, "sourceMap": true, "outDir": "./dist", "strict": true, "noImplicitAny": false, "noUnusedLocals": true, "noUnusedParameters": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "forceConsistentCasingInFileNames": true}, "compileOnSave": false, "include": ["./src/**/*", "./src/**/*.json"], "exclude": ["node_modules"]}
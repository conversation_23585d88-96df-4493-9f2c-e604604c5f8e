{"name": "corp-wl-vhs-bff", "version": "6.7.0", "description": "[![Build status](http://corp-jenkins01.compute.br-sao-1.cvccorp.cloud:8080/view/01-%20BACK/job/MS/job/MS-corp-wl-vhs-bff/badge/icon)] (http://corp-jenkins01.compute.br-sao-1.cvccorp.cloud:8080/view/01-%20BACK/job/MS/job/MS-corp-wl-vhs-bff/) [![Quality Gate](http://sonar.services.cvc.com.br/api/project_badges/measure?project=corp-wl-vhs-bff&metric=alert_status)](http://sonar.services.cvc.com.br/dashboard?id=corp-wl-vhs-bff) [![Coverage](http://sonar.services.cvc.com.br/api/project_badges/measure?project=corp-wl-vhs-bff&metric=coverage)](http://sonar.services.cvc.com.br/component_measures?id=corp-wl-vhs-bff&metric=Coverage) [![Maintainnability](http://sonar.services.cvc.com.br/api/project_badges/measure?project=corp-wl-vhs-bff&metric=sqale_rating)] (http://sonar.services.cvc.com.br/component_measures?id=corp-wl-vhs-bff&metric=Maintainability) [![Security](http://sonar.services.cvc.com.br/api/project_badges/measure?project=corp-wl-vhs-bff&metric=security_rating)] (http://sonar.services.cvc.com.br/component_measures?id=corp-wl-vhs-bff&metric=Security)", "main": "index.js", "license": "MIT", "scripts": {"build:dev": "rimraf ./dist && ./node_modules/typescript/bin/tsc --watch", "dev": "cross-env NODE_ENV=qa NODE_DATASOURCE_ORACLE_USERNAME=BFF_VHS NODE_DATASOURCE_ORACLE_PASSWORD=bffvhs2014cvc# SPRING_DECRYPTY=wsftyuio095fr@15 node -r ts-node/register ./src/index.ts", "qa": "cross-env NODE_ENV=qa NODE_DATASOURCE_ORACLE_USERNAME=BFF_VHS NODE_DATASOURCE_ORACLE_PASSWORD=bffvhs2014cvc# SPRING_DECRYPTY=wsftyuio095fr@15 node -r ts-node/register ./src/index.ts", "build": "rimraf ./dist && ./node_modules/typescript/bin/tsc", "start": "node dist/index.js", "tsc": "tsc"}, "repository": {"type": "git", "url": "http://git.cvc.com.br/Desenvolvimento-MS/corp-wl-vhs/corp-wl-vhs-bff"}, "author": "", "devDependencies": {"@types/aes-js": "^3.1.1", "@types/async-redis": "^1.1.1", "@types/atob": "^2.1.2", "@types/aws-sdk": "^2.7.0", "@types/axios": "^0.14.0", "@types/bluebird": "^3.5.32", "@types/btoa": "^1.2.3", "@types/bunyan": "^1.8.8", "@types/compression": "^1.0.1", "@types/consul": "^0.23.34", "@types/cors": "^2.8.6", "@types/crypto-js": "^3.1.47", "@types/cryptr": "^4.0.1", "@types/express": "^4.17.2", "@types/helmet": "^0.0.45", "@types/jsonwebtoken": "^8.3.7", "@types/jwt-decode": "^2.2.1", "@types/moment": "^2.13.0", "@types/node": "^13.5.0", "@types/redis": "^2.8.32", "@types/swagger-ui-express": "^4.1.1", "@types/uuid": "^3.4.7", "eslint": "^6.8.0", "eslint-plugin-import": "^2.20.0", "jsdoc": "^3.6.3", "nodemon": "^2.0.2", "rimraf": "^3.0.2", "ts-node": "^8.6.2", "tsoa": "^2.5.13", "typescript": "^3.7.5"}, "dependencies": {"@log4js-node/gelf": "^1.0.2", "@nestcloud/boot": "^0.7.5", "@nestcloud/common": "^0.7.5", "@nestcloud/config": "^0.7.5", "@nestcloud/consul": "^0.7.5", "@nestcloud/consul-config": "^0.4.4", "@nestcloud/http": "^0.7.1", "@nestcloud/logger": "^0.7.1", "@nestcloud/service": "^0.7.1", "@nestjs/common": "^7.3.2", "@nestjs/core": "^7.3.2", "@nestjs/platform-express": "^7.0.0", "@nestjs/swagger": "^4.5.12", "@nestjs/terminus": "^7.0.1", "@nestjs/typeorm": "^7.1.0", "@types/oracledb": "^4.2.3", "aes-es": "^3.0.0", "aes-js": "^3.1.2", "async-redis": "^1.1.7", "atob": "^2.1.2", "aws-sdk": "^2.610.0", "axios": "^0.19.2", "btoa": "^1.2.1", "bunyan": "^1.8.15", "compression": "^1.7.4", "consul": "^0.37.0", "cors": "^2.8.5", "cross-env": "^7.0.3", "crypto": "^1.0.1", "crypto-js": "^4.0.0", "cryptr": "^6.0.2", "custom-env": "^1.0.2", "exceljs": "^3.8.0", "express": "^4.17.1", "gelf": "^2.0.1", "helmet": "^3.21.2", "jsonwebtoken": "^9.0.1", "jwt-decode": "^3.1.2", "log4js": "^6.3.0", "moment": "^2.24.0", "node-fetch": "^2.6.0", "oracledb": "^5.0.0", "read-excel-file": "^4.0.5", "redis": "^3.1.2", "swagger-ui-express": "^4.1.3", "tsc": "^1.20150623.0", "uuid": "^3.4.0", "xlsx": "^0.15.5"}}
# Etapa de build da aplicação Node.js
FROM node:14-alpine AS build
WORKDIR /opt/oracle

RUN apk add --no-cache libaio unzip wget zip

RUN wget https://download.oracle.com/otn_software/linux/instantclient/instantclient-basiclite-linuxx64.zip && \
    unzip instantclient-basiclite-linuxx64.zip && \
    rm -f instantclient-basiclite-linuxx64.zip && \
    cd instantclient* && \
    rm -f *jdbc* *occi* *mysql* *jar uidrvci genezi adrci && \
    echo /opt/oracle/instantclient* > /etc/ld.so.conf.d/oracle-instantclient.conf && \
    ldconfig

WORKDIR /corp-wl-vhs-bff
COPY . .
RUN npm install && npm run build
RUN zip -r corp-wl-vhs-bff.zip . -x /node_modules/*

# Etapa de integração com o Veracode
FROM veracode/api-wrapper-java:latest AS veracode
ARG VERACODE_APP_ID
ARG VERACODE_API_KEY
ARG BUILD_ID
COPY --from=build /corp-wl-vhs-bff/corp-wl-vhs-bff.zip /home/<USER>/
RUN java -jar /opt/veracode/api-wrapper.jar \
         -vid $VERACODE_APP_ID \
         -vkey $VERACODE_API_KEY \
         -version $BUILD_ID \
         -action UploadAndScan \
         -createprofile true \
         -appname "corp-wl-vhs-bff" \
         -filepath /home/<USER>/corp-wl-vhs-bff.zip; exit 0;

# Etapa final para criar a imagem de execução
FROM node:14-alpine
WORKDIR /opt/oracle

RUN apk add --no-cache libaio

    # Reutilizar a configuração do Oracle Instant Client
COPY --from=build /opt/oracle /opt/oracle

RUN echo /opt/oracle/instantclient* > /etc/ld.so.conf.d/oracle-instantclient.conf && \
    ldconfig

WORKDIR /corp-wl-vhs-bff
COPY --from=build /corp-wl-vhs-bff /corp-wl-vhs-bff
COPY --from=veracode /home/<USER>/ /corp-wl-vhs-bff/
RUN rm corp-wl-vhs-bff.zip

ENV PORT 8080

EXPOSE 8080

ENTRYPOINT ["npm", "start"]